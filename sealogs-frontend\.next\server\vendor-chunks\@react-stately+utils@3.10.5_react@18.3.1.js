"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-stately+utils@3.10.5_react@18.3.1";
exports.ids = ["vendor-chunks/@react-stately+utils@3.10.5_react@18.3.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-stately+utils@3.10.5_react@18.3.1/node_modules/@react-stately/utils/dist/number.mjs":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-stately+utils@3.10.5_react@18.3.1/node_modules/@react-stately/utils/dist/number.mjs ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clamp: () => (/* binding */ $9446cca9a3875146$export$7d15b64cf5a3a4c4),\n/* harmony export */   roundToStepPrecision: () => (/* binding */ $9446cca9a3875146$export$e1a7b8e69ef6c52f),\n/* harmony export */   snapValueToStep: () => (/* binding */ $9446cca9a3875146$export$cb6e0bb50bc19463),\n/* harmony export */   toFixedNumber: () => (/* binding */ $9446cca9a3875146$export$b6268554fba451f)\n/* harmony export */ });\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ /**\n * Takes a value and forces it to the closest min/max if it's outside. Also forces it to the closest valid step.\n */ function $9446cca9a3875146$export$7d15b64cf5a3a4c4(value, min = -Infinity, max = Infinity) {\n    let newValue = Math.min(Math.max(value, min), max);\n    return newValue;\n}\nfunction $9446cca9a3875146$export$e1a7b8e69ef6c52f(value, step) {\n    let roundedValue = value;\n    let stepString = step.toString();\n    let pointIndex = stepString.indexOf('.');\n    let precision = pointIndex >= 0 ? stepString.length - pointIndex : 0;\n    if (precision > 0) {\n        let pow = Math.pow(10, precision);\n        roundedValue = Math.round(roundedValue * pow) / pow;\n    }\n    return roundedValue;\n}\nfunction $9446cca9a3875146$export$cb6e0bb50bc19463(value, min, max, step) {\n    min = Number(min);\n    max = Number(max);\n    let remainder = (value - (isNaN(min) ? 0 : min)) % step;\n    let snappedValue = $9446cca9a3875146$export$e1a7b8e69ef6c52f(Math.abs(remainder) * 2 >= step ? value + Math.sign(remainder) * (step - Math.abs(remainder)) : value - remainder, step);\n    if (!isNaN(min)) {\n        if (snappedValue < min) snappedValue = min;\n        else if (!isNaN(max) && snappedValue > max) snappedValue = min + Math.floor($9446cca9a3875146$export$e1a7b8e69ef6c52f((max - min) / step, step)) * step;\n    } else if (!isNaN(max) && snappedValue > max) snappedValue = Math.floor($9446cca9a3875146$export$e1a7b8e69ef6c52f(max / step, step)) * step;\n    // correct floating point behavior by rounding to step precision\n    snappedValue = $9446cca9a3875146$export$e1a7b8e69ef6c52f(snappedValue, step);\n    return snappedValue;\n}\nfunction $9446cca9a3875146$export$b6268554fba451f(value, digits, base = 10) {\n    const pow = Math.pow(base, digits);\n    return Math.round(value * pow) / pow;\n}\n\n\n\n//# sourceMappingURL=number.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-stately+utils@3.10.5_react@18.3.1/node_modules/@react-stately/utils/dist/number.mjs\n");

/***/ })

};
;