'use client'

import { useRef, useState, DragEvent, ChangeEvent } from 'react'
import Image from 'next/image'
import { useLazyQuery, useMutation } from '@apollo/client'
import { Loader2 } from 'lucide-react'

import { GET_FILES } from '@/app/lib/graphQL/query'
import { UPDATE_FILE } from '@/app/lib/graphQL/mutation'
import { cn } from '@/app/lib/utils'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { AlertDialogNew } from '@/components/ui'

type FileUploadProps = {
    setDocuments: (files: any[] | ((prev: any[]) => any[])) => void
    text?: string
    subText?: string
    bgClass?: string
    documents: Array<Record<string, any>>
    multipleUpload?: boolean
}

export default function FileUpload({
    setDocuments,
    text = 'Documents and Images',
    subText,
    bgClass = '',
    documents,
    multipleUpload = true,
}: FileUploadProps) {
    /* ------------------------------------------------------- */
    /* state / refs                                            */
    /* ------------------------------------------------------- */
    const [dragActive, setDragActive] = useState(false)
    const [currentFiles, setCurrentFiles] = useState<any[]>([])
    const [openFileNameDialog, setOpenFileNameDialog] = useState(false)
    const [imageLoader, setImageLoader] = useState(false)
    const inputRef = useRef<HTMLInputElement>(null)

    /* ------------------------------------------------------- */
    /* helpers                                                 */
    /* ------------------------------------------------------- */
    const dropZoneClasses = cn(
        'relative flex w-full flex-col items-center justify-center rounded-lg border-2 border-dashed p-6 transition-colors focus-visible:outline-none',
        dragActive
            ? 'bg-curious-blue-50 border-curious-blue-400'
            : 'bg-muted border-border',
        'text-foreground hover:bg-curious-blue-50 hover:border-curious-blue-300',
        'min-h-[10rem] cursor-pointer select-none',
        bgClass,
    )

    const uploadFile = async (file: File) => {
        const formData = new FormData()
        formData.append('FileData', file, file.name.replace(/\s/g, ''))
        try {
            const response = await fetch(
                `${process.env.API_BASE_URL}v2/upload`,
                {
                    method: 'POST',
                    headers: {
                        Authorization: `Bearer ${localStorage.getItem('sl-jwt')}`,
                    },
                    body: formData,
                },
            )
            const data = await response.json()
            await getFileDetails({ variables: { id: [data[0].id] } })
            setImageLoader(false)
        } catch (err) {
            /* eslint-disable-next-line no-console */
            console.error(err)
        }
    }

    /* ------------------------------------------------------- */
    /* apollo hooks                                            */
    /* ------------------------------------------------------- */
    const [getFileDetails] = useLazyQuery(GET_FILES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            setCurrentFiles((prev) => [...prev, response.readFiles.nodes[0]])
            setOpenFileNameDialog(true)
        },
        onError: (error) => console.error(error),
    })

    const [updateFile] = useMutation(UPDATE_FILE, {
        onCompleted: (response) => {
            const updated = response.updateFile
            setDocuments((prev: any[]) =>
                multipleUpload ? [...prev, updated] : [updated],
            )
        },
        onError: (error) => console.error(error),
    })

    /* ------------------------------------------------------- */
    /* event handlers                                          */
    /* ------------------------------------------------------- */
    const handleFiles = (fileList: FileList) => {
        const arr = Array.from(fileList)
        setImageLoader(true)
        arr.forEach(uploadFile)
    }

    const onChange = (e: ChangeEvent<HTMLInputElement>) => {
        if (e.target.files) handleFiles(e.target.files)
    }

    const onDrop = (e: DragEvent<HTMLFormElement>) => {
        e.preventDefault()
        setDragActive(false)
        if (e.dataTransfer.files) handleFiles(e.dataTransfer.files)
    }

    const onDragToggle = (state: boolean) => (e: DragEvent) => {
        e.preventDefault()
        setDragActive(state)
    }

    const handleUpdateFileName = () => {
        currentFiles.forEach((file, index) => {
            const newFileName = (
                document.getElementById(
                    `file-name-${index}`,
                ) as HTMLInputElement
            ).value
            updateFile({
                variables: { input: { id: file.id, title: newFileName } },
            })
        })
        setOpenFileNameDialog(false)
    }

    const openFileExplorer = () => inputRef.current?.click()

    /* ------------------------------------------------------- */
    /* render                                                  */
    /* ------------------------------------------------------- */
    return (
        <div className="w-full pt-4 lg:pt-0">
            <form
                className={dropZoneClasses}
                onSubmit={(e) => e.preventDefault()}
                onDragEnter={onDragToggle(true)}
                onDragOver={onDragToggle(true)}
                onDragLeave={onDragToggle(false)}
                onDrop={onDrop}
                onClick={openFileExplorer}
                aria-label="File uploader drop zone">
                {/* heading */}
                <span className="absolute top-4 left-4 text-xs font-medium uppercase tracking-wider">
                    {text}
                </span>

                {/* hidden native input */}
                <Input
                    ref={inputRef}
                    type="file"
                    className="hidden"
                    multiple={multipleUpload}
                    accept=".xlsx,.xls,image/*,.doc,.docx,.ppt,.pptx,.txt,.pdf"
                    onChange={onChange}
                />

                {/* interactive area */}
                <div className="flex flex-col items-center gap-2 pointer-events-none">
                    <Image
                        src="/sealogs-document_upload.svg"
                        alt="Upload illustration"
                        width={96}
                        height={96}
                        className='relative -translate-x-2.5'
                        priority
                    />
                    {subText && (
                        <span className="text-sm font-medium text-muted-foreground">
                            {subText}
                        </span>
                    )}
                </div>
            </form>

            {/* loader & filename dialog */}
            {imageLoader ? (
                <div className="mt-4 flex items-center justify-center gap-2">
                    <Loader2 className="h-5 w-5 animate-spin text-primary" />
                    <span className="text-sm text-muted-foreground">
                        Uploading...
                    </span>
                </div>
            ) : (
                <AlertDialogNew
                    openDialog={openFileNameDialog}
                    setOpenDialog={setOpenFileNameDialog}
                    handleCreate={handleUpdateFileName}
                    actionText="Save"
                    title="File Name">
                    <div className="space-y-4">
                        {currentFiles.map((file, idx) => (
                            <Label
                                key={file.id}
                                label={`File ${idx + 1} Name`}
                                htmlFor={`file-name-${idx}`}>
                                <Input
                                    id={`file-name-${idx}`}
                                    defaultValue={file.title}
                                    placeholder="Enter file name"
                                />
                            </Label>
                        ))}
                    </div>
                </AlertDialogNew>
            )}
        </div>
    )
}
