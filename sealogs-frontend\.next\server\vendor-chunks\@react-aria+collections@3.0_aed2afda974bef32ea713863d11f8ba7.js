"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-aria+collections@3.0_aed2afda974bef32ea713863d11f8ba7";
exports.ids = ["vendor-chunks/@react-aria+collections@3.0_aed2afda974bef32ea713863d11f8ba7"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+collections@3.0_aed2afda974bef32ea713863d11f8ba7/node_modules/@react-aria/collections/dist/Hidden.mjs":
/*!**********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+collections@3.0_aed2afda974bef32ea713863d11f8ba7/node_modules/@react-aria/collections/dist/Hidden.mjs ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hidden: () => (/* binding */ $f39a9eba43920ace$export$8dc98ba7eadeaa56),\n/* harmony export */   HiddenContext: () => (/* binding */ $f39a9eba43920ace$export$94b6d0abf7d33e8c),\n/* harmony export */   createHideableComponent: () => (/* binding */ $f39a9eba43920ace$export$86427a43e3e48ebb),\n/* harmony export */   useIsHidden: () => (/* binding */ $f39a9eba43920ace$export$b5d7cc18bb8d2b59)\n/* harmony export */ });\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _react_aria_ssr__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-aria/ssr */ \"(ssr)/./node_modules/.pnpm/@react-aria+ssr@3.9.7_react@18.3.1/node_modules/@react-aria/ssr/dist/SSRProvider.mjs\");\n\n\n\n\n/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\n\n// React doesn't understand the <template> element, which doesn't have children like a normal element.\n// It will throw an error during hydration when it expects the firstChild to contain content rendered\n// on the server, when in reality, the browser will have placed this inside the `content` document fragment.\n// This monkey patches the firstChild property for our special hidden template elements to work around this error.\n// See https://github.com/facebook/react/issues/19932\nif (typeof HTMLTemplateElement !== 'undefined') {\n    const getFirstChild = Object.getOwnPropertyDescriptor(Node.prototype, 'firstChild').get;\n    Object.defineProperty(HTMLTemplateElement.prototype, 'firstChild', {\n        configurable: true,\n        enumerable: true,\n        get: function() {\n            if (this.dataset.reactAriaHidden) return this.content.firstChild;\n            else return getFirstChild.call(this);\n        }\n    });\n}\nconst $f39a9eba43920ace$export$94b6d0abf7d33e8c = /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_1__.createContext)(false);\n// Portal to nowhere\nconst $f39a9eba43920ace$var$hiddenFragment = typeof DocumentFragment !== 'undefined' ? new DocumentFragment() : null;\nfunction $f39a9eba43920ace$export$8dc98ba7eadeaa56(props) {\n    let isHidden = (0, react__WEBPACK_IMPORTED_MODULE_1__.useContext)($f39a9eba43920ace$export$94b6d0abf7d33e8c);\n    let isSSR = (0, _react_aria_ssr__WEBPACK_IMPORTED_MODULE_2__.useIsSSR)();\n    if (isHidden) // Don't hide again if we are already hidden.\n    return /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_1__).createElement((0, react__WEBPACK_IMPORTED_MODULE_1__).Fragment, null, props.children);\n    let children = /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_1__).createElement($f39a9eba43920ace$export$94b6d0abf7d33e8c.Provider, {\n        value: true\n    }, props.children);\n    // In SSR, portals are not supported by React. Instead, render into a <template>\n    // element, which the browser will never display to the user. In addition, the\n    // content is not part of the DOM tree, so it won't affect ids or other accessibility attributes.\n    return isSSR ? /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_1__).createElement(\"template\", {\n        \"data-react-aria-hidden\": true\n    }, children) : /*#__PURE__*/ (0, react_dom__WEBPACK_IMPORTED_MODULE_0__.createPortal)(children, $f39a9eba43920ace$var$hiddenFragment);\n}\nfunction $f39a9eba43920ace$export$86427a43e3e48ebb(fn) {\n    let Wrapper = (props, ref)=>{\n        let isHidden = (0, react__WEBPACK_IMPORTED_MODULE_1__.useContext)($f39a9eba43920ace$export$94b6d0abf7d33e8c);\n        if (isHidden) return null;\n        return fn(props, ref);\n    };\n    // @ts-ignore - for react dev tools\n    Wrapper.displayName = fn.displayName || fn.name;\n    return (0, react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(Wrapper);\n}\nfunction $f39a9eba43920ace$export$b5d7cc18bb8d2b59() {\n    return (0, react__WEBPACK_IMPORTED_MODULE_1__.useContext)($f39a9eba43920ace$export$94b6d0abf7d33e8c);\n}\n\n\n\n//# sourceMappingURL=Hidden.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+collections@3.0_aed2afda974bef32ea713863d11f8ba7/node_modules/@react-aria/collections/dist/Hidden.mjs\n");

/***/ })

};
;