'use client'
import React, { useState } from 'react'

import { useMutation } from '@apollo/client'
import Link from 'next/link'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/hooks/use-toast'

import { Building2, Users, FileText, ArrowLeft, X } from 'lucide-react'
import {
    CREATE_SUPPLIER,
    CREATE_SEALOGS_FILE_LINKS,
} from '@/app/lib/graphQL/mutation'
import { useRouter } from 'next/navigation'

import { CREATE_SUPPLIER_CONTACT } from '@/app/lib/graphQL/mutation/CREATE_SUPPLIER_CONTACT'
import { FooterWrapper } from '@/components/footer-wrapper'
import { Card, CardHeader, CardTitle, H4 } from '@/components/ui'
import SupplierContacts, { ISupplierContact } from './supplier-contacts'

export default function NewSupplier({ supplierId }: { supplierId: number }) {
    const router = useRouter()
    const { toast } = useToast()
    const [fileLinks, setFileLinks] = useState<any>([])
    const [linkSelectedOption, setLinkSelectedOption] = useState<any>([])
    const [contactFields, setContactFields] = useState<ISupplierContact[]>([
        {
            name: '',
            email: '',
            phone: '',
        },
    ])

    const handleCreate = async () => {
        const name = (
            document.getElementById('supplier-name') as HTMLInputElement
        ).value
        const website = (
            document.getElementById('supplier-website') as HTMLInputElement
        ).value
        const phone = (
            document.getElementById('supplier-phone') as HTMLInputElement
        ).value
        const email = (
            document.getElementById('supplier-email') as HTMLInputElement
        ).value
        const address = (
            document.getElementById('supplier-address') as HTMLInputElement
        ).value
        const notes = (
            document.getElementById('supplier-notes') as HTMLInputElement
        ).value

        const variables = {
            input: {
                name,
                address,
                website,
                email,
                phone,
                notes,
            },
        }

        if (name === '') {
            return toast({
                variant: 'destructive',
                title: 'Error',
                description: "Please fill supplier's name!",
            })
        }

        if (contactFields.length > 0) {
            const anyContactFieldEmpty = contactFields.filter(
                (contactField) =>
                    contactField.name == '' ||
                    (contactField.phone == '' && contactField.email == ''),
            ).length

            if (anyContactFieldEmpty > 0) {
                return toast({
                    variant: 'destructive',
                    title: 'Error',
                    description: 'Please complete contact data!',
                })
            }
        }

        const response = await mutationCreateSupplier({
            variables,
        })

        const supplierID = response.data?.createSupplier.id ?? 0
        if (supplierID == 0) {
            return toast({
                variant: 'destructive',
                title: 'Error',
                description: 'Error creating new supplier',
            })
        }

        contactFields.forEach(async (element) => {
            const variableContact = { ...element, supplierID }
            delete variableContact.id

            await mutationCreateSupplierContact({
                variables: {
                    input: variableContact,
                },
            })
        })

        router.back()
    }

    const [mutationCreateSupplier, { loading: mutationcreateSupplierLoading }] =
        useMutation(CREATE_SUPPLIER, {
            onCompleted: (response: any) => {},
            onError: (error: any) => {
                console.error('mutationcreateSupplier error', error)
            },
        })

    const [
        mutationCreateSupplierContact,
        { loading: mutationcreateSupplierContactLoading },
    ] = useMutation(CREATE_SUPPLIER_CONTACT, {
        onCompleted: (response: any) => {},
        onError: (error: any) => {
            console.error('mutationcreateSupplierContact error', error)
        },
    })

    const [createSeaLogsFileLinks] = useMutation(CREATE_SEALOGS_FILE_LINKS, {
        onCompleted: (response: any) => {
            const data = response.createSeaLogsFileLinks
            if (data.id > 0) {
                const newLinks = [...fileLinks, data]
                setFileLinks(newLinks)
                linkSelectedOption
                    ? setLinkSelectedOption([
                          ...linkSelectedOption,
                          { label: data.link, value: data.id },
                      ])
                    : setLinkSelectedOption([
                          { label: data.link, value: data.id },
                      ])
            }
        },
        onError: (error: any) => {
            console.error('createSeaLogsFileLinksEntry error', error)
        },
    })

    const handleDeleteLink = (link: any) => {
        setLinkSelectedOption(linkSelectedOption.filter((l: any) => l !== link))
    }

    const linkItem = (link: any) => {
        return (
            <div className="flex justify-between align-middle mr-2 w-fit">
                <Link href={link.label} target="_blank" className="ml-2 ">
                    {link.label}
                </Link>
                <div className="ml-2 ">
                    <Button
                        variant="ghost"
                        size="icon"
                        iconLeft={X}
                        iconOnly
                        onClick={() => handleDeleteLink(link)}
                    />
                </div>
            </div>
        )
    }

    return (
        <>
            <Card className="space-y-8">
                {/* Header */}
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Building2 className="h-6 w-6" />
                        <H4>New Supplier</H4>
                    </CardTitle>
                </CardHeader>
                {/* Basic Information Section */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div className="space-y-4">
                        <h3 className="text-lg font-medium flex items-center gap-2 text-primary">
                            <Building2 className="h-5 w-5" />
                            Company Information
                        </h3>
                        <p className="text-muted-foreground text-sm leading-relaxed">
                            Enter the basic information about the supplier
                            company including contact details and address.
                        </p>
                    </div>

                    <div className="col-span-2 space-y-5">
                        <Label
                            label="Company Name"
                            htmlFor="supplier-name"
                            className="text-sm font-medium">
                            <Input
                                id="supplier-name"
                                type="text"
                                placeholder="Supplier name"
                                className="w-full"
                            />
                        </Label>

                        <Label
                            label="Website"
                            htmlFor="supplier-website"
                            className="text-sm font-medium">
                            <Input
                                id="supplier-website"
                                type="text"
                                placeholder="Company website"
                                className="w-full"
                                onKeyDown={async (event) => {
                                    if (event.key === 'Enter') {
                                        const inputValue = (
                                            event.target as HTMLInputElement
                                        ).value
                                        await createSeaLogsFileLinks({
                                            variables: {
                                                input: {
                                                    link: inputValue,
                                                },
                                            },
                                        })
                                        toast({
                                            title: 'Website added',
                                            description: `Added ${inputValue} to supplier links`,
                                        })
                                    }
                                }}
                            />
                        </Label>

                        {/* Website Links */}
                        {(linkSelectedOption || fileLinks).length > 0 && (
                            <div className="w-full">
                                <Label
                                    label="Linked Websites"
                                    className="text-sm font-medium">
                                    <div className="flex flex-wrap gap-2">
                                        {linkSelectedOption
                                            ? linkSelectedOption.map(
                                                  (link: {
                                                      value: string | number
                                                      label: string
                                                  }) => (
                                                      <div
                                                          key={link.value}
                                                          className="inline-block">
                                                          {linkItem(link)}
                                                      </div>
                                                  ),
                                              )
                                            : fileLinks.map(
                                                  (link: {
                                                      value:
                                                          | React.Key
                                                          | null
                                                          | undefined
                                                  }) => (
                                                      <div
                                                          key={link.value}
                                                          className="inline-block">
                                                          {linkItem(link)}
                                                      </div>
                                                  ),
                                              )}
                                    </div>
                                </Label>
                            </div>
                        )}

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <Label
                                label="Phone Number"
                                htmlFor="supplier-phone"
                                className="text-sm font-medium">
                                <Input
                                    id="supplier-phone"
                                    type="text"
                                    placeholder="Phone number"
                                    className="w-full"
                                />
                            </Label>

                            <Label
                                label="Email Address"
                                htmlFor="supplier-email"
                                className="text-sm font-medium">
                                <Input
                                    id="supplier-email"
                                    type="email"
                                    placeholder="Email address"
                                    className="w-full"
                                />
                            </Label>
                        </div>

                        <Label
                            label="Address"
                            htmlFor="supplier-address"
                            className="text-sm font-medium">
                            <Textarea
                                id="supplier-address"
                                rows={3}
                                placeholder="Supplier address"
                                className="w-full resize-none"
                            />
                        </Label>
                    </div>
                </div>

                <Separator />

                {/* Contacts Section */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div className="space-y-4">
                        <h3 className="text-lg font-medium flex items-center gap-2 text-primary">
                            <Users className="h-5 w-5" />
                            Contact Persons
                        </h3>
                        <p className="text-muted-foreground text-sm leading-relaxed">
                            Enter the contact details (name, phone, and email)
                            of the supplier's representative.
                        </p>
                    </div>

                    <div className="col-span-2">
                        <SupplierContacts
                            data={contactFields}
                            setData={setContactFields}
                        />
                    </div>
                </div>

                <Separator />

                {/* Notes Section */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div className="space-y-4">
                        <h3 className="text-lg font-medium flex items-center gap-2 text-primary">
                            <FileText className="h-5 w-5" />
                            Additional Notes
                        </h3>
                        <p className="text-muted-foreground text-sm leading-relaxed">
                            Add any additional information about this supplier
                            that might be useful.
                        </p>
                    </div>

                    <div className="col-span-2">
                        <Label
                            label="Notes"
                            htmlFor="supplier-notes"
                            className="text-sm font-medium">
                            <Textarea
                                id="supplier-notes"
                                rows={5}
                                placeholder="Enter any additional notes about this supplier..."
                                className="w-full resize-none"
                            />
                        </Label>
                    </div>
                </div>
            </Card>

            <FooterWrapper className="mt-5">
                <Button
                    variant="back"
                    iconLeft={ArrowLeft}
                    onClick={() => router.back()}>
                    Cancel
                </Button>
                <Button onClick={handleCreate}>Create Supplier</Button>
            </FooterWrapper>
        </>
    )
}
