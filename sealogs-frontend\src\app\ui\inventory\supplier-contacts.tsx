import { Plus, X } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'

export interface ISupplierContact {
    id?: number
    name: string
    phone?: string
    email?: string
}

interface IComponentProps {
    data: ISupplierContact[]
    setData: React.Dispatch<React.SetStateAction<ISupplierContact[]>>
}

function SupplierContacts({ data, setData }: IComponentProps) {
    const addItem = () => {
        setData((prev) => {
            return [
                ...prev,
                {
                    name: '',
                    phone: '',
                    email: '',
                },
            ]
        })
    }

    const removeItem = (index: number) => {
        setData((prev) => {
            const newData = [...prev]
            newData.splice(index, 1)
            return newData
        })
    }

    const onValueChange = <K extends keyof ISupplierContact>(
        field: K,
        index: number,
        value: ISupplierContact[K],
    ) => {
        setData((prev) => {
            const newData = [...prev]

            newData[index][field] = value

            return newData
        })
    }

    return (
        <div className="space-y-5">
            {data.map((item, index) => (
                <div
                    key={index}
                    className={`space-y-4 ${index > 0 ? 'border-t pt-4' : ''}`}>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Label
                            label="Contact Name"
                            htmlFor={`supplier-contact_name${index}`}
                            className="text-sm font-medium">
                            <Input
                                id={`supplier-contact_name${index}`}
                                type="text"
                                placeholder="Contact Name"
                                value={item.name}
                                onChange={(e) =>
                                    onValueChange(
                                        'name',
                                        index,
                                        e.target.value,
                                    )
                                }
                                className="w-full"
                            />
                        </Label>

                        <Label
                            label="Contact Phone"
                            htmlFor={`supplier-contact_phone${index}`}
                            className="text-sm font-medium">
                            <Input
                                id={`supplier-contact_phone${index}`}
                                type="text"
                                placeholder="Contact Phone"
                                value={item.phone}
                                onChange={(e) =>
                                    onValueChange(
                                        'phone',
                                        index,
                                        e.target.value,
                                    )
                                }
                                className="w-full"
                            />
                        </Label>

                        <Label
                            label="Contact Email"
                            htmlFor={`supplier-contact_email${index}`}
                            className="text-sm font-medium">
                            <Input
                                id={`supplier-contact_email${index}`}
                                type="email"
                                value={item.email}
                                placeholder="Contact Email"
                                onChange={(e) =>
                                    onValueChange(
                                        'email',
                                        index,
                                        e.target.value,
                                    )
                                }
                                className="w-full"
                            />
                        </Label>
                    </div>

                    {index > 0 && (
                        <div className="flex justify-end">
                            <Button
                                variant="destructive"
                                size="sm"
                                iconLeft={X}
                                onClick={() => removeItem(index)}>
                                Remove Contact
                            </Button>
                        </div>
                    )}
                </div>
            ))}

            <div className="flex justify-end mt-4">
                <Button
                    variant="outline"
                    iconLeft={Plus}
                    onClick={addItem}>
                    Add Contact
                </Button>
            </div>
        </div>
    )
}

export default SupplierContacts
